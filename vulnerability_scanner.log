2025-05-29 20:31:53,686 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPConnectionPool(host='testphp.vulnweb.com', port=80): Read timed out. (read timeout=5)")': /.env
2025-05-29 20:32:04,957 - ERROR - خطأ في اختبار النموذج: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-05-29 20:32:04,961 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /web.config
2025-05-29 20:32:04,963 - ERROR - خطأ في اختبار النموذج: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-05-29 20:32:04,964 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /login.php
2025-05-29 20:32:04,967 - ERROR - خطأ في اختبار النموذج: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-05-29 20:32:08,368 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002A79097D280>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /search.php?test=query
2025-05-29 20:32:10,312 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002A785787E90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /login.php
2025-05-29 20:32:10,384 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002A78C36BF20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /web.config
2025-05-29 20:32:12,986 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000002A785786F00>, 'Connection to testphp.vulnweb.com timed out. (connect timeout=8)')': /search.php?test=query
2025-05-29 20:32:12,987 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000002A7857861E0>, 'Connection to testphp.vulnweb.com timed out. (connect timeout=8)')': /search.php?test=query
2025-05-29 20:39:33,025 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000025F4E1D6630>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed')': /
2025-05-29 20:39:37,723 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000025F4E653770>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed')': /
2025-05-29 20:46:15,429 - INFO - 🚀 بدء الاختبار الموحد للثغرة: SQL Injection في http://testphp.vulnweb.com/login.php
2025-05-29 20:46:15,430 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:15,513 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/artists.php
2025-05-29 20:46:15,516 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:15,526 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/guestbook.php
2025-05-29 20:46:15,527 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:15,546 - INFO - 🚀 بدء الاختبار الموحد للثغرة: Path Traversal في http://testphp.vulnweb.com/hpp
2025-05-29 20:46:15,554 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:15,622 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/disclaimer.php
2025-05-29 20:46:15,623 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:15,733 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com
2025-05-29 20:46:15,733 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:16,195 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/index.php
2025-05-29 20:46:16,196 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:16,211 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/cart.php
2025-05-29 20:46:16,212 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:17,063 - INFO - 🚀 بدء الاختبار الموحد للثغرة: Path Traversal في http://testphp.vulnweb.com/Mod_Rewrite_Shop
2025-05-29 20:46:17,065 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:17,200 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/search.php
2025-05-29 20:46:17,201 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 20:46:20,301 - INFO - ❌ فشل اختبار PoC - لا يوجد تأثير مؤكد
2025-05-29 20:46:20,304 - INFO - 🚀 بدء الاختبار الموحد للثغرة: File Inclusion في http://testphp.vulnweb.com/login.php
2025-05-29 20:46:20,304 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:50,845 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/guestbook.php
2025-05-29 21:03:50,852 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:51,207 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/artists.php
2025-05-29 21:03:51,207 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:51,300 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/categories.php
2025-05-29 21:03:51,300 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:51,368 - INFO - 🚀 بدء الاختبار الموحد للثغرة: Path Traversal في http://testphp.vulnweb.com/Mod_Rewrite_Shop
2025-05-29 21:03:51,368 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:51,450 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/cart.php
2025-05-29 21:03:51,450 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:51,488 - INFO - 🚀 بدء الاختبار الموحد للثغرة: SQL Injection في http://testphp.vulnweb.com/AJAX/index.php
2025-05-29 21:03:51,488 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:51,630 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/search.php
2025-05-29 21:03:51,630 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:51,652 - INFO - 🚀 بدء الاختبار الموحد للثغرة: SQL Injection في http://testphp.vulnweb.com/userinfo.php
2025-05-29 21:03:51,652 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:51,685 - INFO - 🚀 بدء الاختبار الموحد للثغرة: CSRF في http://testphp.vulnweb.com/Templates/main_dynamic_template.dwt.php
2025-05-29 21:03:51,686 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:51,719 - INFO - 🚀 بدء الاختبار الموحد للثغرة: External_vuln_model_Vulnerability في http://testphp.vulnweb.com
2025-05-29 21:03:51,720 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:03:57,549 - INFO - ❌ فشل اختبار PoC - لا يوجد تأثير مؤكد
2025-05-29 21:03:57,554 - INFO - 🚀 بدء الاختبار الموحد للثغرة: Cross-Site Scripting في http://testphp.vulnweb.com/AJAX/index.php
2025-05-29 21:03:57,556 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
2025-05-29 21:04:01,377 - INFO - ❌ فشل اختبار PoC - لا يوجد تأثير مؤكد
2025-05-29 21:04:01,378 - INFO - 🚀 بدء الاختبار الموحد للثغرة: File Inclusion في http://testphp.vulnweb.com/userinfo.php
2025-05-29 21:04:01,380 - INFO - 🧪 تنفيذ اختبار PoC للثغرة...
